Module
  name: game_interface
  description: Create the game interface, where the ingame stuff starts
  author: OTClient team
  website: https://github.com/edubart/otclient
  sandboxed: true
  scripts: [ widgets/uigamemap, gameinterface ]
  load-later:
    - game_buttons
    - game_hotkeys
    - game_questlog
    - game_textmessage
    - game_console
    - game_outfit
    - game_containers
    - game_minimap
    - game_npctrade
    - game_textwindow
    - game_playertrade
    - game_bugreport
    - game_playerdeath
    - game_playermount
    - game_ruleviolation
    - game_market
    - game_modaldialog
    - game_walking
    - game_shop
    - game_itemselector
    - client_textedit
    - game_shaders
    - game_screenshake
    - game_conditions
    - game_playerpanel
  @onLoad: init()
  @onUnload: terminate()
