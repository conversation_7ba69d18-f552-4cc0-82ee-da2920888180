﻿  animatedtext.cpp
  animator.cpp
  client.cpp
  container.cpp
  creature.cpp
  creatures.cpp
  effect.cpp
  game.cpp
  healthbars.cpp
  houses.cpp
  item.cpp
  itemtype.cpp
  lightview.cpp
  localplayer.cpp
  luafunctions_client.cpp
  luavaluecasts_client.cpp
  map.cpp
  mapio.cpp
  mapview.cpp
  minimap.cpp
  missile.cpp
  outfit.cpp
  player.cpp
  pngunpacker.cpp
  protocolcodes.cpp
  protocolgame.cpp
  protocolgameparse.cpp
  protocolgamesend.cpp
  spritemanager.cpp
  statictext.cpp
  thing.cpp
  thingtype.cpp
  thingtypemanager.cpp
  tile.cpp
  towns.cpp
  uicreature.cpp
  uiitem.cpp
  uimap.cpp
  uimapanchorlayout.cpp
  uiminimap.cpp
  uiprogressrect.cpp
  uisprite.cpp
  adaptiverenderer.cpp
  application.cpp
  asyncdispatcher.cpp
  binarytree.cpp
  clock.cpp
  config.cpp
  configmanager.cpp
  eventdispatcher.cpp
  filestream.cpp
  graphicalapplication.cpp
  logger.cpp
  module.cpp
  modulemanager.cpp
  resourcemanager.cpp
  scheduledevent.cpp
  timer.cpp
  event.cpp
  animatedtexture.cpp
  apngloader.cpp
  atlas.cpp
  bitmapfont.cpp
  cachedtext.cpp
  coordsbuffer.cpp
  drawcache.cpp
  drawqueue.cpp
  fontmanager.cpp
  framebuffer.cpp
  framebuffermanager.cpp
  graph.cpp
  graphics.cpp
  hardwarebuffer.cpp
  image.cpp
  painter.cpp
  paintershaderprogram.cpp
  shader.cpp
  shadermanager.cpp
  shaderprogram.cpp
  textrender.cpp
  texture.cpp
  texturemanager.cpp
  http.cpp
  session.cpp
  websocket.cpp
  mouse.cpp
  lbitlib.cpp
  luaexception.cpp
  luainterface.cpp
  luaobject.cpp
  luavaluecasts.cpp
  luafunctions.cpp
  connection.cpp
  inputmessage.cpp
  outputmessage.cpp
  packet_player.cpp
  packet_recorder.cpp
  protocol.cpp
  server.cpp
  otmldocument.cpp
  otmlemitter.cpp
  otmlexception.cpp
  otmlnode.cpp
  otmlparser.cpp
  platform.cpp
  platformwindow.cpp
  win32crashhandler.cpp
  win32platform.cpp
  win32window.cpp
  proxy.cpp
  proxy_client.cpp
  combinedsoundsource.cpp
  oggsoundfile.cpp
  soundbuffer.cpp
  soundchannel.cpp
  soundfile.cpp
  soundmanager.cpp
  soundsource.cpp
  streamsoundsource.cpp
  demangle.cpp
  math.cpp
  net.cpp
  string.cpp
  time.cpp
  uri.cpp
  uianchorlayout.cpp
  uiboxlayout.cpp
  uigridlayout.cpp
  uihorizontallayout.cpp
  uilayout.cpp
  uimanager.cpp
  uitextedit.cpp
  uitranslator.cpp
  uiverticallayout.cpp
  uiwidget.cpp
  uiwidgetbasestyle.cpp
  uiwidgetimage.cpp
  uiwidgettext.cpp
  color.cpp
  crypt.cpp
  extras.cpp
  stats.cpp
  qrcodegen.c
  tinystr.cpp
  tinyxml.cpp
  tinyxmlerror.cpp
  tinyxmlparser.cpp
  main.cpp
     Creating library C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\../otclient_gl.lib and object C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\../otclient_gl.exp
  Generating code
  Finished generating code
  otclient.vcxproj -> C:\Users\<USER>\OneDrive\Desktop\projekt\client\otclient_gl.exe
